# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development server**: `npm run dev` (uses Turbopack for faster builds)
- **Build**: `npm run build`
- **Start production**: `npm run start`
- **Linting**: `npm run lint` (uses Biome)
- **Fix linting issues**: `npm run lint:fix`
- **Format code**: `npm run format` (uses Biome with tab indentation)
- **Deploy to Cloudflare**: `npm run deploy`
- **Preview deployment**: `npm run preview`
- **Generate Cloudflare types**: `npm run cf-typegen`

## Project Architecture

### Tech Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4 with shadcn/ui components
- **Linting/Formatting**: Biome (replaces ESLint/Prettier)
- **State Management**: Zustand (stores directory is empty but configured)
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Radix UI primitives with shadcn/ui
- **Deployment**: Cloudflare Pages with OpenNext adapter

### Directory Structure
- `src/app/` - Next.js App Router pages and layouts
- `src/components/auth/` - Authentication-related components
- `src/components/ui/` - Reusable UI components (shadcn/ui)
- `src/lib/` - Utility functions and shared logic
- `src/stores/` - Zustand state management stores

### Authentication System Architecture
The app uses a route-based authentication system with two distinct user flows:

#### Route Groups
- `(auth)/(new-user)/` - For new user registration and account activation
  - Uses `AuthSignupLayout` component
  - Includes: sign-up, account-activation pages
- `(auth)/(user)/` - For existing user authentication
  - Uses `AuthUserLayout` component  
  - Includes: sign-in, forgot-password, reset-password, register pages

#### Key Components
All auth components are exported from `src/components/auth/index.ts`:
- `RegisterForm` - Main registration form
- `SignInForm` - User login form
- `ForgotPasswordForm` - Password reset request
- `ResetPasswordForm` - New password submission
- `AccountActivation` - Email verification flow
- `CheckEmail` - Email confirmation instructions
- `AuthSignupLayout` & `AuthUserLayout` - Layout wrappers

### Component Conventions
- Uses shadcn/ui component system with "new-york" style
- CSS variables enabled for theming
- Components use `@/` path alias for imports
- Tab indentation and double quotes (enforced by Biome)
- All components are TypeScript with strict typing

### Cloudflare Integration
- Configured for deployment on Cloudflare Pages
- Uses OpenNext adapter for Next.js compatibility
- Cloudflare environment types generated via `cf-typegen`
- Development mode includes Cloudflare context support

### Form Handling
- React Hook Form for form state management
- Zod for schema validation and type safety
- Custom form components in `src/components/ui/form.tsx`
- Phone input with country selection support
- Accessible form controls with proper labeling

### Styling System
- Tailwind CSS v4 with CSS variables
- Custom utility classes via `tw-animate-css`
- Dark mode support via `next-themes`
- Consistent spacing and typography via Geist fonts
- Component variants handled by `class-variance-authority`