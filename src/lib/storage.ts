import type { User } from "@/types/auth";

// Storage keys
const STORAGE_KEYS = {
	TOKEN: "pukpara_token",
	USER: "pukpara_user",
	REFRESH_TOKEN: "pukpara_refresh_token",
} as const;

// Safe storage interface for SSR compatibility
interface SafeStorage {
	getItem: (key: string) => string | null;
	setItem: (key: string, value: string) => void;
	removeItem: (key: string) => void;
}

// Create a safe storage wrapper for SSR
const createSafeStorage = (): SafeStorage => {
	const isClient = typeof window !== "undefined";
	
	return {
		getItem: (key: string) => {
			if (!isClient) return null;
			try {
				return localStorage.getItem(key);
			} catch {
				return null;
			}
		},
		setItem: (key: string, value: string) => {
			if (!isClient) return;
			try {
				localStorage.setItem(key, value);
			} catch {
				// Handle storage errors silently
			}
		},
		removeItem: (key: string) => {
			if (!isClient) return;
			try {
				localStorage.removeItem(key);
			} catch {
				// Handle storage errors silently
			}
		},
	};
};

const storage = createSafeStorage();

// Token management
export const tokenStorage = {
	get: (): string | null => {
		return storage.getItem(STORAGE_KEYS.TOKEN);
	},
	
	set: (token: string): void => {
		storage.setItem(STORAGE_KEYS.TOKEN, token);
	},
	
	remove: (): void => {
		storage.removeItem(STORAGE_KEYS.TOKEN);
	},
	
	isValid: (): boolean => {
		const token = tokenStorage.get();
		if (!token) return false;
		
		try {
			// Basic JWT structure validation
			const parts = token.split(".");
			if (parts.length !== 3) return false;
			
			// Decode payload to check expiration
			const payload = JSON.parse(atob(parts[1]));
			const now = Math.floor(Date.now() / 1000);
			
			return payload.exp > now;
		} catch {
			return false;
		}
	},
};

// User data management
export const userStorage = {
	get: (): User | null => {
		const userData = storage.getItem(STORAGE_KEYS.USER);
		if (!userData) return null;
		
		try {
			return JSON.parse(userData) as User;
		} catch {
			return null;
		}
	},
	
	set: (user: User): void => {
		storage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
	},
	
	remove: (): void => {
		storage.removeItem(STORAGE_KEYS.USER);
	},
	
	update: (updates: Partial<User>): void => {
		const currentUser = userStorage.get();
		if (currentUser) {
			const updatedUser = { ...currentUser, ...updates };
			userStorage.set(updatedUser);
		}
	},
};

// Complete authentication clear
export const clearAuthData = (): void => {
	tokenStorage.remove();
	userStorage.remove();
	storage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
	return tokenStorage.isValid() && userStorage.get() !== null;
};

// Get authentication header value
export const getAuthHeader = (): string | null => {
	const token = tokenStorage.get();
	return token ? `Bearer ${token}` : null;
};

// Initialize auth state from storage
export const initializeAuthFromStorage = () => {
	return {
		token: tokenStorage.get(),
		user: userStorage.get(),
		isAuthenticated: isAuthenticated(),
	};
};