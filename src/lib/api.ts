import axios, { AxiosError, AxiosResponse } from "axios";

// Base API configuration
const API_BASE_URL = "http://pukparabootrapper-dev.eba-344m2e93.us-east-1.elasticbeanstalk.com";

// Create axios instance with default configuration
export const apiClient = axios.create({
	baseURL: API_BASE_URL,
	timeout: 30000,
	headers: {
		"Content-Type": "application/json",
	},
});

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
	(config) => {
		// Get token from localStorage or your preferred storage
		const token = typeof window !== "undefined" ? localStorage.getItem("pukpara_token") : null;
		
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}
		
		return config;
	},
	(error) => {
		return Promise.reject(error);
	}
);

// Response interceptor for global error handling
apiClient.interceptors.response.use(
	(response: AxiosResponse) => {
		return response;
	},
	(error: AxiosError) => {
		// Handle global errors
		if (error.response?.status === 401) {
			// Unauthorized - clear token and redirect to login
			if (typeof window !== "undefined") {
				localStorage.removeItem("pukpara_token");
				localStorage.removeItem("pukpara_user");
				window.location.href = "/sign-in";
			}
		}
		
		return Promise.reject(error);
	}
);

// API response wrapper type
export interface ApiResponse<T = any> {
	data: T;
	message: string;
	statusCode: number;
}

// Error type for API exceptions
export interface PukparaException {
	statusCode: number;
	message: string;
	details: string;
}

// Helper function to handle API errors
export const handleApiError = (error: AxiosError): string => {
	if (error.response?.data) {
		const errorData = error.response.data as PukparaException;
		return errorData.message || "An error occurred";
	}
	
	if (error.code === "ECONNABORTED") {
		return "Request timeout. Please try again.";
	}
	
	if (error.code === "ERR_NETWORK") {
		return "Network error. Please check your internet connection.";
	}
	
	return error.message || "An unexpected error occurred";
};

// Helper function to extract response data
export const extractApiData = <T>(response: AxiosResponse<ApiResponse<T>>): T => {
	return response.data.data;
};