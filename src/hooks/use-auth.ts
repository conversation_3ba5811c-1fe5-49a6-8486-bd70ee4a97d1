import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "sonner";
import { AuthService } from "@/services/auth-service";
import { useAuthStore } from "@/stores/auth-store";
import { handleApiError } from "@/lib/api";
import type {
	UserLoginRequest,
	UserRegistrationRequest,
	ResetPasswordRequest,
	ChangePasswordRequest,
} from "@/types/auth";

// Query keys
export const AUTH_QUERY_KEYS = {
	verifyToken: ["auth", "verify-token"] as const,
} as const;

/**
 * Login mutation hook
 */
export function useLogin() {
	const router = useRouter();
	const login = useAuthStore((state) => state.login);
	
	return useMutation({
		mutationFn: AuthService.login,
		onSuccess: (data) => {
			// Store authentication data
			login(data);
			
			// Show success message
			toast.success("Welcome back to Pukpara!");
			
			// Redirect to dashboard or home
			router.push("/"); // Adjust route as needed
		},
		onError: (error) => {
			const message = handleApiError(error);
			toast.error(message || "Login failed. Please try again.");
		},
	});
}

/**
 * Registration mutation hook
 */
export function useRegister() {
	const router = useRouter();
	
	return useMutation({
		mutationFn: AuthService.register,
		onSuccess: () => {
			toast.success("Account created successfully! Please check your email to activate your account.");
			router.push("/account-activation");
		},
		onError: (error) => {
			const message = handleApiError(error);
			toast.error(message || "Registration failed. Please try again.");
		},
	});
}

/**
 * Password reset request mutation hook
 */
export function useRequestPasswordReset() {
	return useMutation({
		mutationFn: AuthService.requestPasswordReset,
		onSuccess: () => {
			toast.success("Password reset email sent! Please check your inbox.");
		},
		onError: (error) => {
			const message = handleApiError(error);
			toast.error(message || "Failed to send password reset email. Please try again.");
		},
	});
}

/**
 * Password reset validation mutation hook
 */
export function useValidatePasswordReset() {
	const router = useRouter();
	
	return useMutation({
		mutationFn: AuthService.validatePasswordReset,
		onSuccess: () => {
			toast.success("Password reset successfully! You can now log in with your new password.");
			router.push("/sign-in");
		},
		onError: (error) => {
			const message = handleApiError(error);
			toast.error(message || "Failed to reset password. Please try again.");
		},
	});
}

/**
 * Change password mutation hook
 */
export function useChangePassword() {
	return useMutation({
		mutationFn: AuthService.changePassword,
		onSuccess: () => {
			toast.success("Password changed successfully!");
		},
		onError: (error) => {
			const message = handleApiError(error);
			toast.error(message || "Failed to change password. Please try again.");
		},
	});
}

/**
 * Logout mutation hook
 */
export function useLogout() {
	const queryClient = useQueryClient();
	const logout = useAuthStore((state) => state.logout);
	
	return useMutation({
		mutationFn: async () => {
			// Clear React Query cache
			queryClient.clear();
			
			// Clear auth store and storage
			logout();
		},
		onSuccess: () => {
			toast.success("Logged out successfully");
		},
	});
}

/**
 * Token verification query hook
 */
export function useVerifyToken() {
	const token = useAuthStore((state) => state.token);
	
	return useQuery({
		queryKey: AUTH_QUERY_KEYS.verifyToken,
		queryFn: AuthService.verifyToken,
		enabled: !!token,
		staleTime: 5 * 60 * 1000, // 5 minutes
		retry: false,
	});
}

/**
 * Auth initialization hook
 */
export function useInitializeAuth() {
	const initializeAuth = useAuthStore((state) => state.initializeAuth);
	const isLoading = useAuthStore((state) => state.isLoading);
	
	// Initialize auth on mount
	useEffect(() => {
		initializeAuth();
	}, [initializeAuth]);
	
	return { isLoading };
}