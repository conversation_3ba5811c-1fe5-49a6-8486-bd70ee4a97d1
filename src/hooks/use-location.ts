import { useQuery } from "@tanstack/react-query";
import { LocationService } from "@/services/location-service";
import type { PagedSearchFilter } from "@/types/auth";

// Query keys
export const LOCATION_QUERY_KEYS = {
	regions: ["locations", "regions"] as const,
	districts: ["locations", "districts"] as const,
	districtsByRegion: (regionId: string) => ["locations", "districts", "region", regionId] as const,
	searchDistricts: (filter: PagedSearchFilter) => ["locations", "districts", "search", filter] as const,
} as const;

/**
 * Hook to fetch all regions
 */
export function useRegions() {
	return useQuery({
		queryKey: LOCATION_QUERY_KEYS.regions,
		queryFn: LocationService.getRegions,
		staleTime: 10 * 60 * 1000, // 10 minutes (regions don't change often)
		retry: 3,
	});
}

/**
 * Hook to fetch districts by region
 */
export function useDistrictsByRegion(regionId: string, enabled = true) {
	return useQuery({
		queryKey: LOCATION_QUERY_KEYS.districtsByRegion(regionId),
		queryFn: () => LocationService.getDistrictsByRegion(regionId),
		enabled: enabled && !!regionId,
		staleTime: 10 * 60 * 1000, // 10 minutes
		retry: 3,
	});
}

/**
 * Hook to search districts with pagination
 */
export function useSearchDistricts(filter: PagedSearchFilter, enabled = true) {
	return useQuery({
		queryKey: LOCATION_QUERY_KEYS.searchDistricts(filter),
		queryFn: () => LocationService.searchDistricts(filter),
		enabled,
		staleTime: 5 * 60 * 1000, // 5 minutes
		retry: 3,
	});
}

/**
 * Hook to fetch all districts (convenience hook)
 */
export function useAllDistricts() {
	return useQuery({
		queryKey: LOCATION_QUERY_KEYS.districts,
		queryFn: LocationService.getAllDistricts,
		staleTime: 10 * 60 * 1000, // 10 minutes
		retry: 3,
	});
}