import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { User, LoginResponse } from "@/types/auth";
import {
	tokenStorage,
	userStorage,
	clearAuthData,
	initializeAuthFromStorage,
} from "@/lib/storage";

interface AuthState {
	// State
	user: User | null;
	token: string | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	
	// Actions
	login: (response: LoginResponse, user?: User) => void;
	logout: () => void;
	updateUser: (updates: Partial<User>) => void;
	setLoading: (loading: boolean) => void;
	initializeAuth: () => void;
	clearAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
	devtools(
		(set, get) => ({
			// Initial state
			user: null,
			token: null,
			isAuthenticated: false,
			isLoading: true,
			
			// Actions
			login: (response: LoginResponse, user?: User) => {
				// Store token
				tokenStorage.set(response.token);
				
				// Create user object from response if not provided
				const userData: User = user || {
					id: response.userId,
					firstname: "",
					lastname: "",
					email: response.userName,
					phoneNumber: "",
					address: "",
					districtId: "",
					tenantId: response.tenantId,
					isEnabled: true,
					roles: [],
					permissions: [],
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
				};
				
				// Store user data
				userStorage.set(userData);
				
				// Update store state
				set({
					user: userData,
					token: response.token,
					isAuthenticated: true,
					isLoading: false,
				});
			},
			
			logout: () => {
				// Clear storage
				clearAuthData();
				
				// Reset store state
				set({
					user: null,
					token: null,
					isAuthenticated: false,
					isLoading: false,
				});
				
				// Redirect to login page
				if (typeof window !== "undefined") {
					window.location.href = "/sign-in";
				}
			},
			
			updateUser: (updates: Partial<User>) => {
				const currentUser = get().user;
				if (currentUser) {
					const updatedUser = { ...currentUser, ...updates };
					
					// Update storage
					userStorage.update(updates);
					
					// Update store
					set({ user: updatedUser });
				}
			},
			
			setLoading: (loading: boolean) => {
				set({ isLoading: loading });
			},
			
			initializeAuth: () => {
				set({ isLoading: true });
				
				const authData = initializeAuthFromStorage();
				
				set({
					user: authData.user,
					token: authData.token,
					isAuthenticated: authData.isAuthenticated,
					isLoading: false,
				});
			},
			
			clearAuth: () => {
				clearAuthData();
				set({
					user: null,
					token: null,
					isAuthenticated: false,
					isLoading: false,
				});
			},
		}),
		{
			name: "auth-store",
		},
	),
);

// Selector hooks for better performance
export const useAuth = () => useAuthStore((state) => ({
	user: state.user,
	token: state.token,
	isAuthenticated: state.isAuthenticated,
	isLoading: state.isLoading,
}));

export const useAuthActions = () => useAuthStore((state) => ({
	login: state.login,
	logout: state.logout,
	updateUser: state.updateUser,
	setLoading: state.setLoading,
	initializeAuth: state.initializeAuth,
	clearAuth: state.clearAuth,
}));