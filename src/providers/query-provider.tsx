"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useState } from "react";

interface QueryProviderProps {
	children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
	const [queryClient] = useState(
		() =>
			new QueryClient({
				defaultOptions: {
					queries: {
						// Prevent refetching on window focus in development
						refetchOnWindowFocus: process.env.NODE_ENV === "production",
						// Retry failed requests
						retry: (failureCount, error: any) => {
							// Don't retry on 4xx errors except 408 (timeout)
							if (error?.response?.status >= 400 && error?.response?.status < 500) {
								return error?.response?.status === 408 && failureCount < 2;
							}
							// Retry on network errors and 5xx errors up to 3 times
							return failureCount < 3;
						},
						// Default stale time
						staleTime: 1000 * 60, // 1 minute
					},
					mutations: {
						// Global error handling can be added here if needed
						onError: (error: any) => {
							console.error("Mutation error:", error);
						},
					},
				},
			}),
	);

	return (
		<QueryClientProvider client={queryClient}>
			{children}
			{process.env.NODE_ENV === "development" && (
				<ReactQueryDevtools initialIsOpen={false} />
			)}
		</QueryClientProvider>
	);
}