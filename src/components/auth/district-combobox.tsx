"use client";

import { <PERSON><PERSON><PERSON>, ChevronsUpDown } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { useAllDistricts } from "@/hooks/use-location";
import { cn } from "@/lib/utils";

interface DistrictComboboxProps {
	control: any;
	name: string;
	label?: string;
	placeholder?: string;
}

export function DistrictCombobox({
	control,
	name,
	label = "District",
	placeholder = "Select your district",
}: DistrictComboboxProps) {
	const [open, setOpen] = useState(false);
	const [searchValue, setSearchValue] = useState("");
	
	// Fetch districts from API
	const { data: districts = [], isLoading, error } = useAllDistricts();

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => (
				<FormItem>
					<FormLabel className="text-sm font-medium text-foreground">
						{label}
					</FormLabel>
					<Popover open={open} onOpenChange={setOpen}>
						<PopoverTrigger asChild>
							<FormControl>
								<Button
									variant="outline"
									role="combobox"
									aria-expanded={open}
									aria-haspopup="listbox"
									disabled={isLoading}
									className={cn(
										"w-full justify-between",
										!field.value && "text-muted-foreground",
									)}
								>
									{isLoading
										? "Loading districts..."
										: field.value
										? districts.find((district) => district.id === field.value)
												?.name
										: placeholder}
									<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
								</Button>
							</FormControl>
						</PopoverTrigger>
						<PopoverContent className="w-full p-0" align="start">
							<Command shouldFilter={false}>
								<CommandInput
									placeholder="Search district..."
									value={searchValue}
									onValueChange={setSearchValue}
									disabled={isLoading}
								/>
								<CommandList>
									{isLoading ? (
										<div className="py-6 text-center text-sm">Loading districts...</div>
									) : error ? (
										<div className="py-6 text-center text-sm text-destructive">
											Failed to load districts. Please try again.
										</div>
									) : (
										<>
											<CommandEmpty>No district found.</CommandEmpty>
											<CommandGroup>
												{districts
													.filter((district) =>
														district.name
															.toLowerCase()
															.includes(searchValue.toLowerCase()),
													)
													.map((district) => (
														<CommandItem
															value={district.id}
															key={district.id}
															onSelect={(currentValue) => {
																if (currentValue === field.value) {
																	field.onChange("");
																} else {
																	field.onChange(currentValue);
																}
																setOpen(false);
																setSearchValue("");
															}}
														>
															<CheckIcon
																className={cn(
																	"mr-2 h-4 w-4",
																	district.id === field.value
																		? "opacity-100"
																		: "opacity-0",
																)}
															/>
															{district.name}
														</CommandItem>
													))}
											</CommandGroup>
										</>
									)}
								</CommandList>
							</Command>
						</PopoverContent>
					</Popover>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}