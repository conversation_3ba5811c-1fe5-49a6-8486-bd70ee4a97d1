"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Mail } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";

export function AccountActivation() {
	return (
		<Card className="border-0 shadow-none mt-6">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				<div className="flex items-center justify-center mb-6">
					<div className="relative">
						<div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
							<CheckCircle className="w-8 h-8 text-primary" />
						</div>
						<div className="absolute -top-1 -right-1 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
							<Clock className="w-3 h-3 text-primary-foreground" />
						</div>
					</div>
				</div>

				<CardTitle className="text-xl font-semibold text-foreground leading-7 text-center">
					Account Created Successfully!
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5 text-center">
					Your account has been created and is pending activation
				</CardDescription>
			</CardHeader>

			<CardContent className="p-0 mt-6">
				<div className="space-y-6">
					{/* Status Information */}
					<div className="bg-muted/50 rounded-lg p-4 space-y-3">
						<div className="flex items-start space-x-3">
							<Mail className="w-5 h-5 text-primary mt-0.5" />
							<div className="space-y-1">
								<p className="text-sm font-medium text-foreground">
									Waiting for Admin Approval
								</p>
								<p className="text-xs text-muted-foreground">
									Our administrators will review and activate your account.
									You'll receive an email notification once your account is
									approved.
								</p>
							</div>
						</div>
					</div>

					{/* What's Next */}
					<div className="space-y-3">
						<h3 className="text-sm font-medium text-foreground">
							What happens next?
						</h3>
						<div className="space-y-2">
							<div className="flex items-start space-x-3">
								<div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
								<p className="text-sm text-muted-foreground">
									Admin reviews your registration details
								</p>
							</div>
							<div className="flex items-start space-x-3">
								<div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
								<p className="text-sm text-muted-foreground">
									Account gets activated upon approval
								</p>
							</div>
							<div className="flex items-start space-x-3">
								<div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
								<p className="text-sm text-muted-foreground">
									You'll receive an email with login instructions
								</p>
							</div>
						</div>
					</div>

					{/* Action Buttons */}
					<div className="space-y-3">
						<Button asChild className="w-full" size="lg">
							<Link href="/sign-in">Go to Sign In</Link>
						</Button>

						<Button variant="outline" asChild className="w-full" size="lg">
							<Link href="/">Back to Home</Link>
						</Button>
					</div>

					{/* Contact Support */}
					<div className="text-center pt-4 border-t">
						<p className="text-xs text-muted-foreground">
							Need help?{" "}
							<Link
								href="/contact"
								className="text-primary hover:underline font-medium"
							>
								Contact Support
							</Link>
						</p>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
