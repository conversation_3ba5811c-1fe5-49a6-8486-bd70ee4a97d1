"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { Button } from "@/components/ui/button";
import { useLogin } from "@/hooks/use-auth";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
	username: z.string().min(1, {
		message: "Email is required.",
	}).email({
		message: "Please enter a valid email address.",
	}),
	password: z
		.string()
		.min(1, {
			message: "Password is required.",
		}),
	keepSignedIn: z.boolean(),
});

export function SignInForm() {
	// Login mutation
	const loginMutation = useLogin();

	// Define form
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			username: "",
			password: "",
			keepSignedIn: false,
		},
	});

	// Submit handler
	function onSubmit(values: z.infer<typeof formSchema>) {
		// Extract login data (API expects username and password only)
		const loginData = {
			username: values.username,
			password: values.password,
		};
		
		loginMutation.mutate(loginData);
	}

	return (
		<Card className="border-0 shadow-none mt-8">
			<CardHeader className="space-y-2 text-left lg:text-left p-0">
				<CardTitle className="text-xl font-semibold text-foreground leading-7">
					Welcome back to Pukpara
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					Sign in to continue managing your farm operations
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-6">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
						{/* Email Field */}
						<FormField
							control={form.control}
							name="username"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Email
									</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											disabled={loginMutation.isPending}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Password Field */}
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Enter your password"
											disabled={loginMutation.isPending}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Keep me signed in & Forgot password */}
						<div className="flex items-center justify-between">
							<FormField
								control={form.control}
								name="keepSignedIn"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-y-0">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={loginMutation.isPending}
											/>
										</FormControl>
										<div className="leading-none">
											<FormLabel className="text-sm font-normal text-foreground">
												Keep me signed in
											</FormLabel>
										</div>
									</FormItem>
								)}
							/>
							<Link
								href="/forgot-password"
								className="text-sm text-primary hover:underline"
							>
								Forgot password?
							</Link>
						</div>

						{/* Sign In Button */}
						<Button 
							type="submit" 
							className="w-full mt-6" 
							size="lg"
							disabled={loginMutation.isPending}
						>
							{loginMutation.isPending ? "Signing in..." : "Sign In"}
						</Button>
					</form>
				</Form>

				{/* Sign Up Link */}
				<div className="mt-8 text-center">
					<span className="text-sm text-muted-foreground">
						New to Pukpara?{" "}
						<Link
							href="/register"
							className="text-primary hover:underline font-medium"
						>
							Create an account
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
