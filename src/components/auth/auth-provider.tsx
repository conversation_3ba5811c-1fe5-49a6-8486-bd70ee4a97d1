"use client";

import { useEffect } from "react";
import { useAuthStore } from "@/stores/auth-store";

interface AuthProviderProps {
	children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
	const initializeAuth = useAuthStore((state) => state.initializeAuth);

	useEffect(() => {
		// Initialize authentication state from storage on app load
		initializeAuth();
	}, [initializeAuth]);

	return <>{children}</>;
}