// Authentication Request Types
export interface UserLoginRequest {
	username: string;
	password: string;
}

export interface UserRegistrationRequest {
	firstname: string;
	lastname: string;
	email: string;
	phoneNumber: string;
	districtId: string;
	address: string;
	password: string;
	confirmPassword: string;
}

export interface ResetPasswordRequest {
	token: string;
	password: string;
}

export interface ChangePasswordRequest {
	oldPassword: string;
	newPassword: string;
}

// Authentication Response Types
export interface LoginResponse {
	message: string;
	statusCode: number;
	token: string;
	userName: string;
	userId: string;
	modules: string[];
	tenantId: string;
}

export interface AuthResponse {
	message: string;
	statusCode: number;
}

// User and Permission Types
export interface User {
	id: string;
	firstname: string;
	lastname: string;
	email: string;
	phoneNumber: string;
	address: string;
	districtId: string;
	tenantId: string;
	isEnabled: boolean;
	roles: Role[];
	permissions: AppPermission[];
	createdAt: string;
	updatedAt: string;
}

export interface Role {
	id: string;
	name: string;
	description: string;
	permissions: AppPermission[];
}

export interface AppPermission {
	id: string;
	name: string;
	description: string;
}

// Location Types
export interface Region {
	id: string;
	name: string;
	code: string;
}

export interface District {
	id: string;
	name: string;
	code: string;
	regionId: string;
	region?: Region;
}

// Pagination Types
export interface PagedSearchFilter {
	pageNumber: number;
	pageSize: number;
	searchQuery?: string;
}

export interface PaginatedResponse<T> {
	data: T[];
	totalCount: number;
	pageNumber: number;
	pageSize: number;
	totalPages: number;
}

// Tenant Types
export interface Tenant {
	id: string;
	workspaceName: string;
	adminUserId: string;
	status: string;
	disablingReasons?: string;
	createdAt: string;
	updatedAt: string;
}

export interface CreateUserRequest {
	firstname: string;
	lastname: string;
	email: string;
	phoneNumber: string;
	roleId: string;
	password: string;
	address: string;
	districtId: string;
}

export interface UpdateUserStatusRequest {
	isEnabled: boolean;
}

export interface CreateRoleRequest {
	name: string;
	description: string;
}

export interface UpdateRoleRequest {
	description: string;
	name: string;
}

export interface AddPermissionsToRoleRequest {
	permissionIds: string[];
}

export interface UpdatePermissionDescriptionRequest {
	description: string;
}

export interface UpdateTenantRequest {
	adminUserId: string;
	workspaceName: string;
}

export interface UpdateTenantStatusRequest {
	status: string;
	disablingReasons: string;
}