import { apiClient } from "@/lib/api";
import type {
	UserLoginRequest,
	UserRegistrationRequest,
	LoginResponse,
	AuthResponse,
	ResetPasswordRequest,
	ChangePasswordRequest,
} from "@/types/auth";

export class AuthService {
	/**
	 * User login
	 */
	static async login(data: UserLoginRequest): Promise<LoginResponse> {
		const response = await apiClient.post<LoginResponse>("/api/auth/login", data);
		return response.data;
	}

	/**
	 * User registration
	 */
	static async register(data: UserRegistrationRequest): Promise<AuthResponse> {
		const response = await apiClient.post<AuthResponse>("/api/auth/register", data);
		return response.data;
	}

	/**
	 * Request password reset
	 */
	static async requestPasswordReset(email: string): Promise<AuthResponse> {
		const response = await apiClient.post<AuthResponse>(
			`/api/auth/reset-password/${encodeURIComponent(email)}`
		);
		return response.data;
	}

	/**
	 * Validate reset token and set new password
	 */
	static async validatePasswordReset(data: ResetPasswordRequest): Promise<AuthResponse> {
		const response = await apiClient.post<AuthResponse>(
			"/api/auth/reset-password/validate",
			data
		);
		return response.data;
	}

	/**
	 * Change password (authenticated user)
	 */
	static async changePassword(data: ChangePasswordRequest): Promise<AuthResponse> {
		const response = await apiClient.put<AuthResponse>("/api/auth/change-password", data);
		return response.data;
	}

	/**
	 * Logout user (client-side only, no API call needed)
	 */
	static logout(): void {
		// Clear local storage and redirect
		if (typeof window !== "undefined") {
			localStorage.removeItem("pukpara_token");
			localStorage.removeItem("pukpara_user");
			window.location.href = "/sign-in";
		}
	}

	/**
	 * Verify if current token is valid
	 */
	static async verifyToken(): Promise<boolean> {
		try {
			// Make a simple authenticated request to verify token
			await apiClient.get("/api/users"); // Adjust endpoint as needed
			return true;
		} catch {
			return false;
		}
	}
}