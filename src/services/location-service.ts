import { apiClient } from "@/lib/api";
import type {
	Region,
	District,
	PagedSearchFilter,
	PaginatedResponse,
} from "@/types/auth";

export class LocationService {
	/**
	 * Get all regions
	 */
	static async getRegions(): Promise<Region[]> {
		const response = await apiClient.get<Region[]>("/api/locations/regions");
		return response.data;
	}

	/**
	 * Get districts by region ID
	 */
	static async getDistrictsByRegion(regionId: string): Promise<District[]> {
		const response = await apiClient.get<District[]>(
			`/api/locations/regions/${regionId}/districts`
		);
		return response.data;
	}

	/**
	 * Search districts with pagination
	 */
	static async searchDistricts(
		filter: PagedSearchFilter
	): Promise<PaginatedResponse<District>> {
		const response = await apiClient.post<PaginatedResponse<District>>(
			"/api/locations/districts",
			filter
		);
		return response.data;
	}

	/**
	 * Get all districts (convenience method)
	 */
	static async getAllDistricts(): Promise<District[]> {
		const filter: PagedSearchFilter = {
			pageNumber: 1,
			pageSize: 1000, // Large number to get all districts
			searchQuery: "",
		};
		
		const response = await this.searchDistricts(filter);
		return response.data;
	}
}