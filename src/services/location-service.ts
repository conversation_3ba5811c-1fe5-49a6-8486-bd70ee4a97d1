import { apiClient } from "@/lib/api";
import type {
	Region,
	District,
	PagedSearchFilter,
	PaginatedResponse,
} from "@/types/auth";

export class LocationService {
	/**
	 * Get all regions
	 */
	static async getRegions(): Promise<Region[]> {
		const response = await apiClient.get<Region[]>("/api/locations/regions");
		return response.data;
	}

	/**
	 * Get districts by region ID
	 */
	static async getDistrictsByRegion(regionId: string): Promise<District[]> {
		const response = await apiClient.get<District[]>(
			`/api/locations/regions/${regionId}/districts`,
		);
		return response.data;
	}

	/**
	 * Search districts with pagination
	 */
	static async searchDistricts(
		filter: PagedSearchFilter,
	): Promise<PaginatedResponse<District>> {
		const response = await apiClient.post<PaginatedResponse<District>>(
			"/api/locations/districts",
			filter,
		);
		return response.data;
	}

	/**
	 * Get all districts (convenience method)
	 * Fetches all districts by using a large page size to get all districts at once
	 */
	static async getAllDistricts(): Promise<District[]> {
		const filter: PagedSearchFilter = {
			pageNumber: 1,
			pageSize: 5000, // Large number to get all districts (Ghana has ~260 districts)
			searchQuery: "", // Empty search to get all
		};

		const response = await this.searchDistricts(filter);
		return response.data;
	}

	/**
	 * Alternative method: Get all districts by fetching from all regions
	 * This can be used as a fallback if the search method doesn't work well
	 */
	static async getAllDistrictsByRegions(): Promise<District[]> {
		// First get all regions
		const regions = await this.getRegions();

		// Then get districts for each region
		const districtPromises = regions.map((region) =>
			this.getDistrictsByRegion(region.id),
		);

		// Wait for all requests to complete
		const districtArrays = await Promise.all(districtPromises);

		// Flatten the arrays into a single array
		return districtArrays.flat();
	}
}
