import { apiClient } from "@/lib/api";
import type { Tenant, PaginatedResponse } from "@/types/auth";

export class TenantService {
	/**
	 * Get tenant by ID
	 */
	static async getTenantById(tenantId: string): Promise<Tenant> {
		// Note: This endpoint might not exist in the current API
		// We may need to use the admin endpoint or create a new one
		const response = await apiClient.get<Tenant>(`/api/tenants/${tenantId}`);
		return response.data;
	}

	/**
	 * Get current user's tenant information
	 * This is a convenience method that could be implemented on the backend
	 */
	static async getCurrentTenant(): Promise<Tenant> {
		const response = await apiClient.get<Tenant>("/api/tenant/current");
		return response.data;
	}

	/**
	 * Check if tenant is active
	 */
	static async isTenantActive(tenantId: string): Promise<boolean> {
		try {
			const tenant = await this.getTenantById(tenantId);
			return tenant.status.toLowerCase() === "active";
		} catch (error) {
			console.error("Error checking tenant status:", error);
			return false;
		}
	}

	/**
	 * Get all tenants (admin only) - using existing admin endpoint
	 */
	static async getAllTenants(params?: {
		status?: string;
		pageNumber?: number;
		pageSize?: number;
		searchQuery?: string;
	}): Promise<PaginatedResponse<Tenant>> {
		const response = await apiClient.get<PaginatedResponse<Tenant>>(
			"/api/admin/tenants",
			{ params }
		);
		return response.data;
	}
}
