# Pukpara Application API Documentation

## Base URL
```
http://pukparabootrapper-dev.eba-344m2e93.us-east-1.elasticbeanstalk.com
```

## API Overview
The Pukpara Application API is a RESTful API built with .NET that provides endpoints for user authentication, user management, role-based access control, location services, and tenant management.

## Authentication Endpoints

### 1. User Login
**POST** `/api/auth/login`

**Request Body:**
```typescript
interface UserLoginRequest {
  username: string;
  password: string;
}
```

**Response (200 OK):**
```typescript
interface LoginResponse {
  message: string;
  statusCode: number;
  token: string;
  userName: string;
  userId: string;
  modules: string[];
  tenantId: string;
}
```

**Error Responses:** 400 (Bad Request), 500 (Internal Server Error)

### 2. User Registration
**POST** `/api/auth/register`

**Request Body:**
```typescript
interface UserRegistrationRequest {
  firstname: string;
  lastname: string;
  email: string;
  phoneNumber: string;
  districtId: string;
  address: string;
  password: string;
  confirmPassword: string;
}
```

**Response (200 OK):**
```typescript
interface Response {
  message: string;
  statusCode: number;
}
```

### 3. Reset Password (Request)
**POST** `/api/auth/reset-password/{email}`

**Parameters:**
- `email` (path parameter): User's email address

**Response (200 OK):**
```typescript
interface Response {
  message: string;
  statusCode: number;
}
```

### 4. Reset Password (Validate & Set New Password)
**POST** `/api/auth/reset-password/validate`

**Request Body:**
```typescript
interface ResetPasswordRequest {
  token: string;
  password: string;
}
```

**Response (200 OK):**
```typescript
interface Response {
  message: string;
  statusCode: number;
}
```

### 5. Change Password
**PUT** `/api/auth/change-password`

**Request Body:**
```typescript
interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
}
```

**Response (200 OK):**
```typescript
interface Response {
  message: string;
  statusCode: number;
}
```

## Location Services

### 1. Get All Regions
**GET** `/api/locations/regions`

**Response (200 OK):** Array of regions

### 2. Get Districts by Region
**GET** `/api/locations/regions/{regionId}/districts`

**Parameters:**
- `regionId` (path parameter): Region identifier

**Response (200 OK):** Array of districts for the specified region

### 3. Search Districts (Paginated)
**POST** `/api/locations/districts`

**Request Body:**
```typescript
interface PagedSearchFilter {
  pageNumber: number;
  pageSize: number;
  searchQuery?: string;
}
```

**Response (200 OK):** Paginated list of districts

## User Management

### 1. Get All Users
**GET** `/api/users`

**Response (200 OK):** Array of users

### 2. Create User
**POST** `/api/users`

**Request Body:**
```typescript
interface CreateUserRequest {
  firstname: string;
  lastname: string;
  email: string;
  phoneNumber: string;
  roleId: string;
  password: string;
  address: string;
  districtId: string;
}
```

### 3. Update User Status
**PATCH** `/api/users/{userId}/status`

**Parameters:**
- `userId` (path parameter): User identifier

**Request Body:**
```typescript
interface UpdateUserStatusRequest {
  isEnabled: boolean;
}
```

### 4. Assign Role to User
**POST** `/api/users/{userId}/roles/{roleId}`

**Parameters:**
- `userId` (path parameter): User identifier
- `roleId` (path parameter): Role identifier

### 5. Remove User Roles
**DELETE** `/api/users/{userId}/roles`

**Parameters:**
- `userId` (path parameter): User identifier

**Request Body:** Role ID as string

### 6. Get User Permissions
**GET** `/api/users/{userId}/permissions`

**Parameters:**
- `userId` (path parameter): User identifier

### 7. Add User Permissions
**POST** `/api/users/{userId}/permissions`

**Parameters:**
- `userId` (path parameter): User identifier

**Request Body:**
```typescript
interface AppPermission {
  id: string;
  name: string;
  description: string;
}
```

### 8. Remove User Permissions
**DELETE** `/api/users/{userId}/permissions`

**Parameters:**
- `userId` (path parameter): User identifier

**Request Body:** Array of permission IDs (string[])

## Role Management

### 1. Get All Roles
**GET** `/api/roles`

**Response (200 OK):** Array of roles

### 2. Create Role
**POST** `/api/roles`

**Request Body:**
```typescript
interface CreateRoleRequest {
  name: string;
  description: string;
}
```

### 3. Get Role by ID
**GET** `/api/roles/{roleId}`

**Parameters:**
- `roleId` (path parameter): Role identifier

### 4. Update Role
**PUT** `/api/roles/{roleId}`

**Parameters:**
- `roleId` (path parameter): Role identifier

**Request Body:**
```typescript
interface UpdateRoleRequest {
  description: string;
  name: string;
}
```

### 5. Delete Role
**DELETE** `/api/roles/{roleId}`

**Parameters:**
- `roleId` (path parameter): Role identifier

### 6. Assign User to Role
**POST** `/api/roles/{roleId}/users/{userId}`

**Parameters:**
- `roleId` (path parameter): Role identifier
- `userId` (path parameter): User identifier

### 7. Remove User from Role
**DELETE** `/api/roles/{roleId}/users/{userId}`

**Parameters:**
- `roleId` (path parameter): Role identifier
- `userId` (path parameter): User identifier

### 8. Get User Roles
**GET** `/api/roles/user/{userId}`

**Parameters:**
- `userId` (path parameter): User identifier

### 9. Get Role Permissions
**GET** `/api/roles/{roleId}/permissions`

**Parameters:**
- `roleId` (path parameter): Role identifier

### 10. Add Permissions to Role
**POST** `/api/roles/{roleId}/permissions`

**Parameters:**
- `roleId` (path parameter): Role identifier

**Request Body:**
```typescript
interface AddPermissionsToRoleRequest {
  permissionIds: string[];
}
```

## Permission Management

### 1. Get All Permissions (Paginated)
**GET** `/api/permissions`

**Query Parameters:**
- `PageNumber` (optional): Page number
- `PageSize` (optional): Page size
- `SearchQuery` (optional): Search term

### 2. Update Permission Description
**PATCH** `/api/permissions/{permissionId}/description`

**Parameters:**
- `permissionId` (path parameter): Permission identifier

**Request Body:**
```typescript
interface UpdatePermissionDescriptionRequest {
  description: string;
}
```

## Tenant Management (Admin)

### 1. Get All Tenants (Paginated)
**GET** `/api/admin/tenants`

**Query Parameters:**
- `Status` (optional): Tenant status filter
- `PageNumber` (optional): Page number
- `PageSize` (optional): Page size
- `SearchQuery` (optional): Search term

### 2. Update Tenant
**POST** `/api/admin/tenants/{tenantId}`

**Parameters:**
- `tenantId` (path parameter): Tenant identifier

**Request Body:**
```typescript
interface UpdateTenantRequest {
  adminUserId: string;
  workspaceName: string;
}
```

### 3. Update Tenant Status
**PATCH** `/api/admin/tenants/{tenantId}/status`

**Parameters:**
- `tenantId` (path parameter): Tenant identifier

**Request Body:**
```typescript
interface UpdateTenantStatusRequest {
  status: string;
  disablingReasons: string;
}
```

### 4. Get Tenant Users (Paginated)
**GET** `/api/admin/tenants/{tenantId}/users`

**Parameters:**
- `tenantId` (path parameter): Tenant identifier

**Query Parameters:**
- `PageNumber` (optional): Page number
- `PageSize` (optional): Page size
- `SearchQuery` (optional): Search term

### 5. Create User in Tenant
**POST** `/api/admin/tenants/{tenantId}/users`

**Parameters:**
- `tenantId` (path parameter): Tenant identifier

**Request Body:** Same as CreateUserRequest

### 6. Delete User from Tenant
**DELETE** `/api/admin/tenants/{tenantId}/users/{userId}`

**Parameters:**
- `tenantId` (path parameter): Tenant identifier
- `userId` (path parameter): User identifier

### 7. Delegate Access to User
**POST** `/api/admin/tenants/{tenantId}/users/{userId}/delegate_access`

**Parameters:**
- `tenantId` (path parameter): Tenant identifier
- `userId` (path parameter): User identifier

## Error Handling

All endpoints return consistent error responses:

```typescript
interface PukparaException {
  statusCode: number;
  message: string;
  details: string;
  // Additional system properties...
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request (validation errors, invalid input)
- `500` - Internal Server Error

## Frontend Implementation Notes

### Authentication Flow
1. Use `/api/auth/login` for user authentication
2. Store the returned `token` for subsequent API calls
3. Include the token in Authorization header: `Bearer {token}`
4. Use `userId` and `tenantId` for user context

### Registration Flow
1. Fetch regions using `/api/locations/regions`
2. Fetch districts for selected region using `/api/locations/regions/{regionId}/districts`
3. Submit registration with `/api/auth/register`

### Password Reset Flow
1. Request reset using `/api/auth/reset-password/{email}`
2. User receives email with reset token
3. Validate token and set new password using `/api/auth/reset-password/validate`

### Data Fetching Patterns
- Most list endpoints support pagination with `PageNumber`, `PageSize`, and `SearchQuery`
- Use appropriate HTTP methods (GET for fetching, POST for creating, PUT/PATCH for updating, DELETE for removing)
- Handle loading states and error states appropriately

### State Management Recommendations
- Store authentication state (token, user info) in global state
- Cache location data (regions, districts) as they're relatively static
- Implement proper error boundaries and loading states
- Use optimistic updates where appropriate for better UX
